#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件连发测试GUI工具 - 修复版本
专门用于测试联通企业邮箱的稳定性
"""

import sys
import smtplib
import imaplib
import email
import time
import threading
import random
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.utils import formatdate, make_msgid
import re

# 随机汉字库 - 常用汉字，避免生僻字
RANDOM_CHINESE_CHARS = [
    '工作', '报告', '会议', '项目', '计划', '总结', '分析', '数据', '系统', '管理',
    '通知', '文件', '资料', '信息', '内容', '结果', '方案', '建议', '意见', '反馈',
    '进展', '状态', '情况', '问题', '解决', '处理', '完成', '开始', '结束', '时间',
    '日期', '安排', '调整', '更新', '修改', '确认', '审核', '批准', '发布', '执行',
    '监控', '检查', '测试', '验证', '评估', '优化', '改进', '提升', '效果', '质量',
    '服务', '客户', '用户', '需求', '功能', '特性', '版本', '升级', '维护', '支持',
    '团队', '部门', '公司', '企业', '组织', '人员', '员工', '领导', '同事', '合作',
    '业务', '流程', '规范', '标准', '制度', '政策', '规定', '要求', '目标', '任务',
    '重要', '紧急', '优先', '关键', '核心', '主要', '基本', '详细', '具体', '明确',
    '及时', '准确', '有效', '成功', '顺利', '正常', '稳定', '可靠', '安全', '保密'
]

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                               QWidget, QLabel, QLineEdit, QPushButton, QTextEdit,
                               QComboBox, QCheckBox, QProgressBar, QGroupBox,
                               QMessageBox, QTabWidget, QSpinBox, QFormLayout)
from PySide6.QtCore import Qt, QThread, Signal
from PySide6.QtGui import QFont, QTextCursor

# 邮箱配置常量
EMAIL_CONFIGS = {
    'enterprise': {
        'name': '联通企业邮箱',
        'smtp_host': 'xcs.mail.chinaunicom.cn',
        'smtp_port': 465,
        'use_ssl': True,
        'username': '<EMAIL>',
        'password': 'OAbbd520.',
        'from_addr': '<EMAIL>',
        'interval': 3.0,  # 企业邮箱发送间隔
        'timeout': 45,    # 企业邮箱超时时间
    },
    'qq_send': {
        'name': 'QQ邮箱(发送端)',
        'smtp_host': 'smtp.qq.com',
        'smtp_port': 465,
        'use_ssl': True,
        'username': '<EMAIL>',
        'password': 'ftyyonprjcetbcei',
        'from_addr': '<EMAIL>',
        'interval': 1.0,  # QQ邮箱发送间隔
        'timeout': 15,    # QQ邮箱超时时间
    },
    'qq_receive': {
        'name': 'QQ邮箱(接收端)',
        'imap_host': 'imap.qq.com',
        'imap_port': 993,
        'use_ssl': True,
        'username': '<EMAIL>',
        'password': 'jfgymtzguazodggd',  # IMAP授权码
        'email': '<EMAIL>',
    }
}

# 测试配置常量
TEST_CONFIGS = {
    'default_email_type': '双邮箱对比测试',  # 默认测试类型
    'default_count': '10封',               # 默认邮件数量
    'default_enable_receive_check': True,   # 默认启用接收检测
    'receive_check_interval': 15,          # 接收检测间隔(秒) - 缩短间隔
    'receive_check_max_times': 20,         # 最大检查次数 - 增加检查次数
    'receive_check_recent_emails': 50,     # 检查最近邮件数量 - 增加检查范围
    'check_all_folders': True,             # 检查所有文件夹（包括垃圾箱）
}

def generate_random_chinese_subject(test_id, email_type, sequence, total, current_time):
    """
    生成随机汉字主题，避免被识别为批量邮件
    格式：随机10-30个汉字 + 日期时间
    """
    # 随机选择10-30个汉字词组
    word_count = random.randint(5, 15)  # 5-15个词组，每个词组2个字，总共10-30个汉字
    selected_words = random.sample(RANDOM_CHINESE_CHARS, word_count)

    # 构造自然的主题
    subject_parts = []

    # 添加一些连接词使主题更自然
    connectors = ['关于', '针对', '有关', '基于', '根据', '按照', '依据']
    actions = ['的通知', '的报告', '的总结', '的分析', '的说明', '的汇报', '的反馈']

    # 随机组合
    if random.choice([True, False]):
        subject_parts.append(random.choice(connectors))

    # 添加随机词组
    subject_parts.extend(selected_words[:word_count//2])

    # 添加动作词
    subject_parts.append(random.choice(actions))

    # 添加剩余词组
    if len(selected_words) > word_count//2:
        subject_parts.extend(selected_words[word_count//2:])

    # 组合主题
    chinese_part = ''.join(subject_parts)

    # 添加日期时间
    date_part = current_time.strftime('%Y年%m月%d日 %H:%M')

    # 最终主题（隐藏测试ID在邮件头中，主题看起来完全正常）
    subject = f"{chinese_part} - {date_part}"

    return subject

def build_test_message(subject, from_addr, to_addr, body, domain=None, x_test_id=None):
    """
    统一构造测试邮件，确保可投递性
    按照邮件标准添加必要的头部信息
    """
    msg = MIMEMultipart('alternative')
    msg['Subject'] = subject
    msg['From'] = from_addr
    msg['To'] = to_addr
    msg['Date'] = formatdate(localtime=True)

    # 以发件域生成 Message-ID，便于反垃圾与去重
    dom = domain or from_addr.split('@')[1]
    msg['Message-ID'] = make_msgid(domain=dom)

    # 添加测试追踪头
    if x_test_id:
        msg['X-Test-ID'] = x_test_id

    # 添加标准邮件头
    msg['X-Mailer'] = 'OutlookExpress/6.0'  # 伪装成常见邮件客户端
    msg['X-Priority'] = '3'  # 正常优先级

    # 添加邮件正文
    msg.attach(MIMEText(body, 'plain', 'utf-8'))

    return msg

class EmailTestWorker(QThread):
    """邮件测试工作线程"""
    progress_updated = Signal(int, str)  # 进度, 消息
    test_completed = Signal(dict)  # 测试结果
    
    def __init__(self, config, count, check_received=False, email_type="联通企业邮箱"):
        super().__init__()
        self.config = config
        self.count = count
        self.check_received = check_received
        self.email_type = email_type
        self.test_id = datetime.now().strftime('%Y%m%d_%H%M%S')
        
    def run(self):
        """执行邮件测试"""
        try:
            result = self.send_batch_emails()
            if self.check_received and result['success_count'] > 0:
                received_result = self.check_received_emails(result['start_time'], result['success_count'])
                result.update(received_result)
            self.test_completed.emit(result)
        except Exception as e:
            self.progress_updated.emit(100, f"❌ 测试失败: {e}")
            
    def send_batch_emails(self):
        """发送批量邮件"""
        if self.email_type == "双邮箱对比测试":
            return self.send_dual_mailbox_test()
        else:
            return self.send_single_mailbox_test()

    def send_single_mailbox_test(self):
        """单邮箱测试"""
        success_count = 0
        failed_count = 0
        send_times = []
        start_time = datetime.now()

        email_type_name = "联通企业邮箱" if "chinaunicom" in self.config['smtp_host'] else "QQ邮箱"
        self.progress_updated.emit(0, f"📬 开始连发测试: {self.count}封邮件 ({email_type_name})")
        self.progress_updated.emit(0, f"🆔 测试ID: {self.test_id}")

        for i in range(self.count):
            try:
                # 创建测试邮件 - 使用随机汉字主题
                current_time = datetime.now()
                email_type = "企业" if "chinaunicom" in self.config['smtp_host'] else "QQ"
                x_test_id = f"{self.test_id}-{email_type}-{i+1:02d}"

                # 生成随机汉字主题
                subject = generate_random_chinese_subject(
                    self.test_id, email_type, i+1, self.count, current_time
                )

                # 测试空内容的效果
                test_content = ""

                msg = build_test_message(
                    subject, self.config['from_addr'], self.config['to_addr'],
                    test_content, domain=self.config['from_addr'].split('@')[1],
                    x_test_id=x_test_id
                )
                
                # 发送邮件 - 添加DSN支持和优化
                send_start = time.time()

                if self.config['use_ssl']:
                    server = smtplib.SMTP_SSL(self.config['smtp_host'], self.config['smtp_port'], timeout=45)
                else:
                    server = smtplib.SMTP(self.config['smtp_host'], self.config['smtp_port'], timeout=45)

                # 明确获取 ESMTP 特性
                server.ehlo()
                server.login(self.config['username'], self.config['password'])

                # 设置DSN（投递状态通知）
                mail_opts, rcpt_opts = [], []
                if hasattr(server, 'esmtp_features') and server.esmtp_features and 'dsn' in server.esmtp_features:
                    mail_opts.append('RET=FULL')
                    rcpt_opts.append('NOTIFY=SUCCESS,FAILURE,DELAY')

                # 发送邮件
                result = server.sendmail(
                    self.config['from_addr'], [self.config['to_addr']],
                    msg.as_string(),
                    mail_options=mail_opts,
                    rcpt_options=rcpt_opts
                )
                server.quit()
                
                send_duration = time.time() - send_start
                send_times.append(send_duration)
                
                if result:
                    self.progress_updated.emit(int((i+1)/self.count*50), f"📧 [{i+1:02d}/{self.count:02d}] 发送完成但有警告 ({send_duration:.1f}s)")
                    self.progress_updated.emit(int((i+1)/self.count*50), f"   主题: {subject}")
                    success_count += 1
                else:
                    self.progress_updated.emit(int((i+1)/self.count*50), f"📧 [{i+1:02d}/{self.count:02d}] 发送成功 ({send_duration:.1f}s)")
                    self.progress_updated.emit(int((i+1)/self.count*50), f"   主题: {subject}")
                    success_count += 1
                
                # 邮件间隔 - 添加抖动避免被识别为批量发送
                if i < self.count - 1:
                    if "chinaunicom" in self.config['smtp_host']:
                        # 企业邮箱：3-6秒随机间隔
                        base_interval = 3.0
                        jitter = random.uniform(0.5, 3.0)
                    else:
                        # QQ邮箱：1-2秒随机间隔
                        base_interval = 1.0
                        jitter = random.uniform(0.3, 1.0)

                    total_interval = base_interval + jitter
                    time.sleep(total_interval)
                    
            except Exception as e:
                self.progress_updated.emit(int((i+1)/self.count*50), f"❌ [{i+1:02d}/{self.count:02d}] 发送失败: {e}")
                failed_count += 1
        
        total_time = (datetime.now() - start_time).total_seconds()
        avg_send_time = sum(send_times) / len(send_times) if send_times else 0
        
        return {
            'test_id': self.test_id,
            'start_time': start_time,
            'count': self.count,
            'success_count': success_count,
            'failed_count': failed_count,
            'total_time': total_time,
            'avg_send_time': avg_send_time,
            'send_success_rate': (success_count/self.count*100) if self.count > 0 else 0,
            'email_type': "联通企业邮箱" if "chinaunicom" in self.config['smtp_host'] else "QQ邮箱"
        }

    def send_dual_mailbox_test(self):
        """双邮箱对比测试"""
        start_time = datetime.now()
        self.progress_updated.emit(0, f"📬 开始双邮箱对比测试: 每个邮箱{self.count}封邮件")
        self.progress_updated.emit(0, f"🆔 测试ID: {self.test_id}")

        # 企业邮箱配置
        enterprise_config = {
            'smtp_host': self.config['smtp_host'],
            'smtp_port': self.config['smtp_port'],
            'use_ssl': self.config['use_ssl'],
            'username': self.config['username'],
            'password': self.config['password'],
            'from_addr': self.config['from_addr'],
            'to_addr': self.config['to_addr']
        }

        # QQ邮箱配置
        qq_send_config = EMAIL_CONFIGS['qq_send']
        qq_config = {
            'smtp_host': qq_send_config['smtp_host'],
            'smtp_port': qq_send_config['smtp_port'],
            'use_ssl': qq_send_config['use_ssl'],
            'username': self.config.get('qq_username', qq_send_config['username']),
            'password': self.config.get('qq_password', qq_send_config['password']),
            'from_addr': self.config.get('qq_username', qq_send_config['from_addr']),
            'to_addr': self.config['to_addr']
        }

        # 发送企业邮箱
        self.progress_updated.emit(10, "📧 开始测试联通企业邮箱...")
        enterprise_result = self.send_emails_with_config(enterprise_config, "企业", 0, 40)

        # 发送QQ邮箱
        self.progress_updated.emit(50, "📧 开始测试QQ邮箱...")
        qq_result = self.send_emails_with_config(qq_config, "QQ", 50, 90)

        total_time = (datetime.now() - start_time).total_seconds()

        return {
            'test_id': self.test_id,
            'start_time': start_time,
            'count': self.count * 2,  # 总数是两倍
            'total_time': total_time,
            'email_type': "双邮箱对比测试",
            'enterprise_result': enterprise_result,
            'qq_result': qq_result,
            'success_count': enterprise_result['success_count'] + qq_result['success_count'],
            'failed_count': enterprise_result['failed_count'] + qq_result['failed_count'],
            'send_success_rate': ((enterprise_result['success_count'] + qq_result['success_count']) / (self.count * 2) * 100)
        }

    def send_emails_with_config(self, config, email_type, progress_start, progress_end):
        """使用指定配置发送邮件"""
        success_count = 0
        failed_count = 0
        send_times = []

        for i in range(self.count):
            try:
                # 创建测试邮件 - 使用随机汉字主题
                current_time = datetime.now()
                x_test_id = f"{self.test_id}-{email_type}-{i+1:02d}"

                # 生成随机汉字主题，完全避免被识别为测试邮件
                subject = generate_random_chinese_subject(
                    self.test_id, email_type, i+1, self.count, current_time
                )

                # 测试空内容的效果
                test_content = ""

                msg = build_test_message(
                    subject, config['from_addr'], config['to_addr'],
                    test_content, domain=config['from_addr'].split('@')[1],
                    x_test_id=x_test_id
                )

                # 发送邮件 - 添加DSN支持和优化
                send_start = time.time()

                if config['use_ssl']:
                    server = smtplib.SMTP_SSL(config['smtp_host'], config['smtp_port'], timeout=45)
                else:
                    server = smtplib.SMTP(config['smtp_host'], config['smtp_port'], timeout=45)

                # 明确获取 ESMTP 特性
                server.ehlo()
                server.login(config['username'], config['password'])

                # 设置DSN（投递状态通知）
                mail_opts, rcpt_opts = [], []
                if hasattr(server, 'esmtp_features') and server.esmtp_features and 'dsn' in server.esmtp_features:
                    mail_opts.append('RET=FULL')
                    rcpt_opts.append('NOTIFY=SUCCESS,FAILURE,DELAY')

                # 发送邮件
                result = server.sendmail(
                    config['from_addr'], [config['to_addr']],
                    msg.as_string(),
                    mail_options=mail_opts,
                    rcpt_options=rcpt_opts
                )
                server.quit()

                send_duration = time.time() - send_start
                send_times.append(send_duration)

                progress = progress_start + int((i+1)/self.count * (progress_end - progress_start))

                # 显示完整的发送主题
                if result:
                    self.progress_updated.emit(progress, f"📧 {email_type}[{i+1:02d}/{self.count:02d}] 发送完成但有警告 ({send_duration:.1f}s)")
                    self.progress_updated.emit(progress, f"   主题: {subject}")
                    success_count += 1
                else:
                    self.progress_updated.emit(progress, f"📧 {email_type}[{i+1:02d}/{self.count:02d}] 发送成功 ({send_duration:.1f}s)")
                    self.progress_updated.emit(progress, f"   主题: {subject}")
                    success_count += 1

                # 邮件间隔 - 添加抖动避免被识别为批量发送
                if i < self.count - 1:
                    if email_type == "企业":
                        base_interval = EMAIL_CONFIGS['enterprise']['interval']
                        # 企业邮箱：3-6秒随机间隔
                        jitter = random.uniform(0.5, 3.0)
                    else:
                        base_interval = EMAIL_CONFIGS['qq_send']['interval']
                        # QQ邮箱：1-2秒随机间隔
                        jitter = random.uniform(0.3, 1.0)

                    total_interval = base_interval + jitter
                    time.sleep(total_interval)

            except Exception as e:
                progress = progress_start + int((i+1)/self.count * (progress_end - progress_start))
                self.progress_updated.emit(progress, f"❌ {email_type}[{i+1:02d}/{self.count:02d}] 发送失败: {e}")
                failed_count += 1

        avg_send_time = sum(send_times) / len(send_times) if send_times else 0

        return {
            'success_count': success_count,
            'failed_count': failed_count,
            'avg_send_time': avg_send_time,
            'send_success_rate': (success_count/self.count*100) if self.count > 0 else 0
        }
    
    def check_received_emails(self, test_start_time, expected_count):
        """检查接收端邮件"""
        self.progress_updated.emit(60, "🔍 开始检查接收端邮件...")
        
        if not self.config.get('imap_password'):
            self.progress_updated.emit(70, "⚠️ 未配置IMAP密码，跳过接收检测")
            return {'received_count': 0, 'missing_count': expected_count, 'loss_rate': 100}
        
        try:
            # 连接IMAP服务器
            mail = imaplib.IMAP4_SSL('imap.qq.com', 993)
            mail.login(self.config['to_addr'], self.config['imap_password'])

            self.progress_updated.emit(70, "✅ IMAP登录成功，开始搜索邮件...")

            # 获取所有文件夹，包括垃圾箱
            status, boxes = mail.list()
            folder_candidates = ['INBOX', 'Junk', 'Spam', '垃圾邮件', '垃圾箱', 'Trash']

            if status == 'OK':
                for line in boxes or []:
                    try:
                        # 解析文件夹名称
                        folder_line = line.decode(errors='ignore')
                        # 提取引号中的文件夹名
                        if '"' in folder_line:
                            name = folder_line.split('"')[-2]
                        else:
                            name = folder_line.split()[-1]

                        # 检查是否是垃圾箱相关文件夹
                        name_lower = name.lower()
                        if any(keyword in name_lower for keyword in ['junk', 'spam', 'trash', '垃圾', 'deleted']):
                            if name not in folder_candidates:
                                folder_candidates.append(name)
                    except Exception:
                        continue

            # 去重保序
            folder_candidates = list(dict.fromkeys(folder_candidates))
            self.progress_updated.emit(72, f"📁 将检查文件夹: {', '.join(folder_candidates[:5])}...")

            # 等待并检查邮件
            max_checks = self.config.get('max_checks', TEST_CONFIGS['receive_check_max_times'])
            check_interval = self.config.get('check_interval', TEST_CONFIGS['receive_check_interval'])
            expected_each = expected_count // 2  # 动态计算每个邮箱的期望数量

            self.progress_updated.emit(75, f"📊 检测配置: 最多检查{max_checks}次, 间隔{check_interval}秒, 每邮箱期望{expected_each}封")

            for check_num in range(max_checks):
                try:
                    # 遍历所有文件夹搜索邮件
                    all_test_emails = {}  # 使用字典按X-Test-ID去重
                    total_found = 0

                    for folder in folder_candidates:
                        try:
                            # 选择文件夹（只读模式）
                            status, _ = mail.select(folder, readonly=True)
                            if status != 'OK':
                                continue

                            # 搜索包含测试ID的邮件
                            status, messages = mail.search(None, 'HEADER', 'Subject', self.test_id)
                            if status != 'OK' or not messages[0]:
                                continue

                            message_ids = messages[0].split()
                            folder_found = len(message_ids)
                            total_found += folder_found

                            if folder_found > 0:
                                self.progress_updated.emit(70 + int(check_num / max_checks * 20),
                                                         f"� {folder}: 找到 {folder_found} 封相关邮件")

                            # 检查每封邮件
                            for msg_id in message_ids:
                                try:
                                    status, msg_data = mail.fetch(msg_id, '(RFC822)')
                                    if status == 'OK':
                                        email_body = msg_data[0][1]
                                        email_message = email.message_from_bytes(email_body)

                                        subject = email_message.get('Subject', '')
                                        from_addr = email_message.get('From', '')
                                        date_str = email_message.get('Date', '')
                                        x_test_id = email_message.get('X-Test-ID', '')
                                        message_id = email_message.get('Message-ID', '')

                                        # 使用X-Test-ID或Message-ID作为唯一标识
                                        unique_id = x_test_id or message_id or f"{subject}-{from_addr}"

                                        # 检查是否是本次测试的邮件
                                        is_test_email = False

                                        # 条件1: 检查X-Test-ID
                                        if x_test_id and self.test_id in x_test_id:
                                            is_test_email = True

                                        # 条件2: 检查测试ID在主题中
                                        elif self.test_id in subject:
                                            is_test_email = True

                                        # 条件3: 检查发件人和时间
                                        elif any(sender in from_addr for sender in [
                                            '<EMAIL>',
                                            '<EMAIL>'
                                        ]):
                                            try:
                                                email_time = email.utils.parsedate_to_datetime(date_str)
                                                time_diff = abs((email_time.replace(tzinfo=None) - test_start_time).total_seconds())
                                                if time_diff < 3600:  # 1小时内
                                                    is_test_email = True
                                            except Exception:
                                                pass

                                        if is_test_email and unique_id not in all_test_emails:
                                            all_test_emails[unique_id] = {
                                                'subject': subject,
                                                'from': from_addr,
                                                'date': date_str,
                                                'folder': folder,
                                                'x_test_id': x_test_id,
                                                'message_id': message_id
                                            }

                                except Exception as e:
                                    continue

                        except Exception:
                            # 文件夹访问失败，继续下一个
                            continue

                    # 分析邮件来源（从去重后的字典中）
                    test_emails = list(all_test_emails.values())
                    enterprise_emails = []
                    qq_emails = []

                    for email_info in test_emails:
                        if '<EMAIL>' in email_info['from']:
                            enterprise_emails.append(email_info)
                        elif '<EMAIL>' in email_info['from']:
                            qq_emails.append(email_info)

                    received_count = len(test_emails)
                    progress = 70 + int(check_num / max_checks * 25)

                    # 显示详细的接收统计
                    self.progress_updated.emit(progress, f"🔍 第{check_num+1}次检查: 收到 {received_count}/{expected_count} 封测试邮件")
                    self.progress_updated.emit(progress, f"  📧 企业邮箱: {len(enterprise_emails)}/{expected_each} 封")
                    self.progress_updated.emit(progress, f"  📧 QQ邮箱: {len(qq_emails)}/{expected_each} 封")

                    # 显示缺失的邮件
                    if len(enterprise_emails) < expected_each:
                        missing_enterprise = expected_each - len(enterprise_emails)
                        self.progress_updated.emit(progress, f"  ❌ 企业邮箱缺失: {missing_enterprise} 封")

                    if len(qq_emails) < expected_each:
                        missing_qq = expected_each - len(qq_emails)
                        self.progress_updated.emit(progress, f"  ❌ QQ邮箱缺失: {missing_qq} 封")

                    # 显示邮件分布情况
                    folder_stats = {}
                    for email_info in test_emails:
                        folder = email_info['folder']
                        folder_stats[folder] = folder_stats.get(folder, 0) + 1

                    if folder_stats:
                        folder_info = ', '.join([f"{folder}:{count}" for folder, count in folder_stats.items()])
                        self.progress_updated.emit(progress, f"  📁 文件夹分布: {folder_info}")

                    # 检查是否收到足够的邮件
                    if received_count >= expected_count:
                        self.progress_updated.emit(90, f"✅ 已收到所有 {expected_count} 封邮件！")
                        break

                    if check_num < max_checks - 1:
                        self.progress_updated.emit(progress, f"⏱️ 等待 {check_interval} 秒后再次检查...")
                        time.sleep(check_interval)

                except Exception as e:
                    self.progress_updated.emit(70, f"❌ 搜索过程出错: {e}")
                    break
                
            # 正确关闭IMAP连接
            try:
                mail.close()
            except Exception:
                pass
            
            try:
                mail.logout()
            except Exception:
                pass
            
            # 最终统计 - 使用最后一次检查的结果
            final_test_emails = list(all_test_emails.values()) if 'all_test_emails' in locals() else []
            final_enterprise = []
            final_qq = []

            for email_info in final_test_emails:
                if '<EMAIL>' in email_info['from']:
                    final_enterprise.append(email_info)
                elif '<EMAIL>' in email_info['from']:
                    final_qq.append(email_info)

            received_count = len(final_test_emails)

            missing_count = expected_count - received_count
            loss_rate = (missing_count / expected_count * 100) if expected_count > 0 else 0

            # 详细统计
            enterprise_received = len(final_enterprise)
            qq_received = len(final_qq)
            expected_each = expected_count // 2
            enterprise_missing = expected_each - enterprise_received
            qq_missing = expected_each - qq_received

            self.progress_updated.emit(95, f"📊 最终统计:")
            self.progress_updated.emit(95, f"  企业邮箱: {enterprise_received}/{expected_each} 封 (缺失{enterprise_missing}封)")
            self.progress_updated.emit(95, f"  QQ邮箱: {qq_received}/{expected_each} 封 (缺失{qq_missing}封)")

            # 显示文件夹分布
            if final_test_emails:
                folder_distribution = {}
                for email_info in final_test_emails:
                    folder = email_info.get('folder', 'Unknown')
                    folder_distribution[folder] = folder_distribution.get(folder, 0) + 1

                folder_info = ', '.join([f"{folder}:{count}" for folder, count in folder_distribution.items()])
                self.progress_updated.emit(95, f"  📁 邮件分布: {folder_info}")

            return {
                'received_count': received_count,
                'missing_count': missing_count,
                'loss_rate': loss_rate,
                'enterprise_received': enterprise_received,
                'enterprise_missing': enterprise_missing,
                'qq_received': qq_received,
                'qq_missing': qq_missing
            }
            
        except Exception as e:
            self.progress_updated.emit(80, f"❌ 接收检测失败: {e}")
            return {'received_count': 0, 'missing_count': expected_count, 'loss_rate': 100}

class EmailTestGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("📬 邮件连发测试工具 (修复版)")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建标签页
        tab_widget = QTabWidget()
        central_widget_layout = QVBoxLayout(central_widget)
        central_widget_layout.addWidget(tab_widget)
        
        # 配置标签页
        config_tab = self.create_config_tab()
        tab_widget.addTab(config_tab, "📧 邮件配置")
        
        # 测试标签页
        test_tab = self.create_test_tab()
        tab_widget.addTab(test_tab, "🚀 连发测试")
        
        # 结果标签页
        result_tab = self.create_result_tab()
        tab_widget.addTab(result_tab, "📊 测试结果")
        
        # 初始化配置
        self.load_default_config()
        
    def create_config_tab(self):
        """创建配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 发送配置
        send_group = QGroupBox("📤 发送邮箱配置")
        send_layout = QFormLayout(send_group)

        # 邮箱类型选择
        self.email_type = QComboBox()
        self.email_type.addItems(["联通企业邮箱", "QQ邮箱(发送端)", "双邮箱对比测试"])
        self.email_type.setCurrentText(TEST_CONFIGS['default_email_type'])  # 默认双邮箱测试
        self.email_type.currentTextChanged.connect(self.on_email_type_changed)

        # 企业邮箱配置
        enterprise_config = EMAIL_CONFIGS['enterprise']
        self.smtp_host = QLineEdit(enterprise_config['smtp_host'])
        self.smtp_port = QSpinBox()
        self.smtp_port.setRange(1, 65535)
        self.smtp_port.setValue(enterprise_config['smtp_port'])
        self.use_ssl = QCheckBox("使用SSL")
        self.use_ssl.setChecked(enterprise_config['use_ssl'])
        self.username = QLineEdit(enterprise_config['username'])
        self.password = QLineEdit(enterprise_config['password'])
        self.password.setEchoMode(QLineEdit.Password)
        self.from_addr = QLineEdit(enterprise_config['from_addr'])
        self.to_addr = QLineEdit(EMAIL_CONFIGS['qq_receive']['email'])

        # QQ发送邮箱配置
        self.qq_group = QGroupBox("📧 QQ邮箱发送配置")
        qq_layout = QFormLayout(self.qq_group)

        qq_config = EMAIL_CONFIGS['qq_send']
        self.qq_username = QLineEdit(qq_config['username'])
        self.qq_password = QLineEdit(qq_config['password'])
        self.qq_password.setEchoMode(QLineEdit.Password)
        self.qq_password.setPlaceholderText("QQ邮箱授权码")

        qq_layout.addRow("QQ邮箱:", self.qq_username)
        qq_layout.addRow("授权码:", self.qq_password)

        self.qq_group.setVisible(True)  # 默认显示（双邮箱测试）

        send_layout.addRow("邮箱类型:", self.email_type)
        send_layout.addRow("SMTP服务器:", self.smtp_host)
        send_layout.addRow("端口:", self.smtp_port)
        send_layout.addRow("", self.use_ssl)
        send_layout.addRow("用户名:", self.username)
        send_layout.addRow("密码:", self.password)
        send_layout.addRow("发件人:", self.from_addr)
        send_layout.addRow("收件人:", self.to_addr)
        
        # 接收配置
        recv_group = QGroupBox("📥 接收检测配置")
        recv_layout = QFormLayout(recv_group)

        self.enable_recv_check = QCheckBox("启用接收端检测")
        self.enable_recv_check.setChecked(TEST_CONFIGS['default_enable_receive_check'])  # 默认启用

        # 接收邮箱配置
        recv_config = EMAIL_CONFIGS['qq_receive']
        self.recv_email = QLineEdit(recv_config['email'])
        self.recv_email.setReadOnly(True)
        self.imap_password = QLineEdit(recv_config['password'])
        self.imap_password.setEchoMode(QLineEdit.Password)

        # 检测间隔配置
        self.check_interval = QSpinBox()
        self.check_interval.setRange(10, 300)
        self.check_interval.setValue(TEST_CONFIGS['receive_check_interval'])
        self.check_interval.setSuffix(" 秒")

        self.max_checks = QSpinBox()
        self.max_checks.setRange(5, 50)
        self.max_checks.setValue(TEST_CONFIGS['receive_check_max_times'])
        self.max_checks.setSuffix(" 次")

        recv_layout.addRow("", self.enable_recv_check)
        recv_layout.addRow("接收邮箱:", self.recv_email)
        recv_layout.addRow("IMAP授权码:", self.imap_password)
        recv_layout.addRow("检测间隔:", self.check_interval)
        recv_layout.addRow("最大检查次数:", self.max_checks)
        
        # 测试按钮
        test_conn_btn = QPushButton("🔍 测试连接")
        test_conn_btn.clicked.connect(self.test_connection)
        
        layout.addWidget(send_group)
        layout.addWidget(self.qq_group)  # 添加QQ邮箱配置组
        layout.addWidget(recv_group)
        layout.addWidget(test_conn_btn)
        layout.addStretch()

        return widget

    def on_email_type_changed(self, email_type):
        """邮箱类型改变时的处理"""
        if email_type == "联通企业邮箱":
            # 显示企业邮箱配置
            config = EMAIL_CONFIGS['enterprise']
            self.smtp_host.setText(config['smtp_host'])
            self.smtp_port.setValue(config['smtp_port'])
            self.use_ssl.setChecked(config['use_ssl'])
            self.username.setText(config['username'])
            self.password.setText(config['password'])
            self.from_addr.setText(config['from_addr'])
            self.to_addr.setText(EMAIL_CONFIGS['qq_receive']['email'])
            self.qq_group.setVisible(False)

        elif email_type == "QQ邮箱(发送端)":
            # 显示QQ邮箱配置
            config = EMAIL_CONFIGS['qq_send']
            self.smtp_host.setText(config['smtp_host'])
            self.smtp_port.setValue(config['smtp_port'])
            self.use_ssl.setChecked(config['use_ssl'])
            self.username.setText(config['username'])
            self.password.setText(config['password'])
            self.from_addr.setText(config['from_addr'])
            self.to_addr.setText(EMAIL_CONFIGS['qq_receive']['email'])
            self.qq_group.setVisible(False)

        elif email_type == "双邮箱对比测试":
            # 显示双邮箱配置
            enterprise_config = EMAIL_CONFIGS['enterprise']
            self.smtp_host.setText(enterprise_config['smtp_host'])
            self.smtp_port.setValue(enterprise_config['smtp_port'])
            self.use_ssl.setChecked(enterprise_config['use_ssl'])
            self.username.setText(enterprise_config['username'])
            self.password.setText(enterprise_config['password'])
            self.from_addr.setText(enterprise_config['from_addr'])
            self.to_addr.setText(EMAIL_CONFIGS['qq_receive']['email'])
            self.qq_group.setVisible(True)  # 显示QQ邮箱配置

    def create_test_tab(self):
        """创建测试标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 测试配置
        test_group = QGroupBox("🚀 测试配置")
        test_layout = QFormLayout(test_group)

        self.email_count = QComboBox()
        self.email_count.addItems(["5封", "8封", "10封", "15封", "20封", "30封"])
        self.email_count.setCurrentText(TEST_CONFIGS['default_count'])

        test_layout.addRow("连发数量:", self.email_count)

        # 控制按钮
        button_layout = QHBoxLayout()

        self.start_btn = QPushButton("🚀 开始连发测试")
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        self.start_btn.clicked.connect(self.start_test)

        self.stop_btn = QPushButton("⏹️ 停止测试")
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        self.stop_btn.clicked.connect(self.stop_test)

        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.stop_btn)
        button_layout.addStretch()

        # 进度显示
        progress_group = QGroupBox("📊 测试进度")
        progress_layout = QVBoxLayout(progress_group)

        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)

        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setFont(QFont("Consolas", 9))

        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.log_text)

        layout.addWidget(test_group)
        layout.addLayout(button_layout)
        layout.addWidget(progress_group)
        layout.addStretch()

        return widget

    def create_result_tab(self):
        """创建结果标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 结果显示
        self.result_text = QTextEdit()
        self.result_text.setFont(QFont("Consolas", 10))
        self.result_text.setReadOnly(True)

        # 操作按钮
        button_layout = QHBoxLayout()

        clear_btn = QPushButton("🗑️ 清空结果")
        clear_btn.clicked.connect(self.clear_results)

        save_btn = QPushButton("💾 保存结果")
        save_btn.clicked.connect(self.save_results)

        button_layout.addWidget(clear_btn)
        button_layout.addWidget(save_btn)
        button_layout.addStretch()

        layout.addWidget(self.result_text)
        layout.addLayout(button_layout)

        return widget

    def load_default_config(self):
        """加载默认配置"""
        pass

    def get_config(self):
        """获取当前配置"""
        config = {
            'smtp_host': self.smtp_host.text(),
            'smtp_port': self.smtp_port.value(),
            'use_ssl': self.use_ssl.isChecked(),
            'username': self.username.text(),
            'password': self.password.text(),
            'from_addr': self.from_addr.text(),
            'to_addr': self.to_addr.text(),
            'imap_password': self.imap_password.text() if self.enable_recv_check.isChecked() else None,
            # 接收检测配置
            'recv_email': self.recv_email.text(),
            'check_interval': self.check_interval.value(),
            'max_checks': self.max_checks.value(),
        }

        # 如果是双邮箱测试，添加QQ邮箱配置
        if self.email_type.currentText() == "双邮箱对比测试":
            config.update({
                'qq_username': self.qq_username.text(),
                'qq_password': self.qq_password.text()
            })

        return config

    def test_connection(self):
        """测试邮件连接"""
        config = self.get_config()

        try:
            # 测试SMTP连接
            if config['use_ssl']:
                server = smtplib.SMTP_SSL(config['smtp_host'], config['smtp_port'], timeout=30)
            else:
                server = smtplib.SMTP(config['smtp_host'], config['smtp_port'], timeout=30)

            server.login(config['username'], config['password'])
            server.quit()

            msg = "✅ SMTP连接测试成功！"

            # 测试IMAP连接（如果启用）
            if self.enable_recv_check.isChecked() and config['imap_password']:
                imap_result = self.test_imap_connection(config['to_addr'], config['imap_password'])
                msg += f"\n{imap_result}"

            QMessageBox.information(self, "连接测试", msg)

        except Exception as e:
            QMessageBox.critical(self, "连接测试失败", f"❌ SMTP连接失败:\n{e}")

    def test_imap_connection(self, email_addr, password):
        """测试IMAP连接"""
        try:
            mail = imaplib.IMAP4_SSL('imap.qq.com', 993)
            mail.login(email_addr, password)

            # 选择收件箱进行测试
            status, _ = mail.select('INBOX')
            if status == 'OK':
                # 获取邮箱信息
                status, count = mail.search(None, 'ALL')
                if status == 'OK':
                    email_count = len(count[0].split()) if count[0] else 0
                    result = f"✅ IMAP连接测试成功！收件箱有 {email_count} 封邮件"
                else:
                    result = "✅ IMAP连接成功，但无法读取邮件数量"
            else:
                result = "✅ IMAP登录成功，但无法选择收件箱"

            # 正确关闭连接
            try:
                mail.close()
            except Exception:
                pass

            try:
                mail.logout()
            except Exception:
                pass

            return result

        except imaplib.IMAP4.error as e:
            return f"❌ IMAP协议错误: {e}"
        except Exception as e:
            return f"❌ IMAP连接失败: {e}"

    def start_test(self):
        """开始测试"""
        config = self.get_config()
        count = int(self.email_count.currentText().replace("封", ""))
        check_received = self.enable_recv_check.isChecked()
        email_type = self.email_type.currentText()

        # 验证配置
        if not all([config['smtp_host'], config['username'], config['password'],
                   config['from_addr'], config['to_addr']]):
            QMessageBox.warning(self, "配置错误", "请填写完整的邮件配置信息")
            return

        # 双邮箱测试需要验证QQ邮箱配置
        if email_type == "双邮箱对比测试":
            if not all([config.get('qq_username'), config.get('qq_password')]):
                QMessageBox.warning(self, "配置错误", "双邮箱测试需要填写完整的QQ邮箱配置")
                return

        if check_received and not config['imap_password']:
            QMessageBox.warning(self, "配置错误", "启用接收检测需要填写QQ邮箱授权码")
            return

        # 启动测试线程
        self.worker = EmailTestWorker(config, count, check_received, email_type)
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.test_completed.connect(self.test_finished)
        self.worker.start()

        # 更新UI状态
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setValue(0)
        self.log_text.clear()

        if email_type == "双邮箱对比测试":
            self.add_log(f"🚀 开始双邮箱对比测试: 每个邮箱{count}封邮件")
        else:
            self.add_log(f"🚀 开始连发测试: {count}封邮件 ({email_type})")

    def stop_test(self):
        """停止测试"""
        if hasattr(self, 'worker') and self.worker.isRunning():
            self.worker.terminate()
            self.worker.wait()

        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.add_log("⏹️ 测试已停止")

    def update_progress(self, progress, message):
        """更新进度"""
        self.progress_bar.setValue(progress)
        self.add_log(message)

    def test_finished(self, result):
        """测试完成"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setValue(100)

        # 生成测试报告
        report = self.generate_report(result)
        self.result_text.setText(report)
        self.add_log("✅ 测试完成！请查看结果标签页")

    def generate_report(self, result):
        """生成测试报告"""
        if result.get('email_type') == "双邮箱对比测试":
            return self.generate_dual_mailbox_report(result)
        else:
            return self.generate_single_mailbox_report(result)

    def generate_single_mailbox_report(self, result):
        """生成单邮箱测试报告"""
        report = f"""
{'='*60}
📊 邮件连发测试报告
{'='*60}

🎯 测试概况:
• 测试ID: {result['test_id']}
• 测试时间: {result['start_time'].strftime('%Y-%m-%d %H:%M:%S')}
• 邮箱类型: {result.get('email_type', '未知')}

📈 发送结果:
• 计划发送: {result['count']} 封
• 成功发送: {result['success_count']} 封
• 发送失败: {result['failed_count']} 封
• 发送成功率: {result['send_success_rate']:.1f}%

⏱️ 时间统计:
• 发送总耗时: {result['total_time']:.1f} 秒
• 平均每封: {result.get('avg_send_time', 0):.1f} 秒
• 发送速度: {result['count']/result['total_time']*60:.1f} 封/分钟
"""

        # 如果有接收检测结果
        if 'received_count' in result:
            report += f"""
📥 接收结果:
• 实际收到: {result['received_count']} 封
• 丢失邮件: {result['missing_count']} 封
• 丢邮件率: {result['loss_rate']:.1f}%
• 端到端成功率: {(result['received_count']/result['count']*100):.1f}%

🏆 综合评估:"""

            overall_success_rate = result['received_count'] / result['count'] * 100
            if overall_success_rate >= 95:
                report += "\n🎉 优秀！邮箱稳定性很好"
            elif overall_success_rate >= 85:
                report += "\n✅ 良好！邮箱稳定性可接受"
            elif overall_success_rate >= 70:
                report += "\n⚠️ 一般！建议优化邮箱配置"
            else:
                report += "\n❌ 较差！强烈建议更换邮箱或优化配置"
        else:
            report += f"""
💡 建议:
请手动检查收件箱 {self.to_addr.text()}
搜索主题包含 '{result['test_id']}' 的邮件
统计实际收到的邮件数量"""

        report += f"\n{'='*60}"
        return report

    def generate_dual_mailbox_report(self, result):
        """生成双邮箱对比测试报告"""
        enterprise = result['enterprise_result']
        qq = result['qq_result']

        report = f"""
{'='*60}
📊 双邮箱对比测试报告
{'='*60}

🎯 测试概况:
• 测试ID: {result['test_id']}
• 测试时间: {result['start_time'].strftime('%Y-%m-%d %H:%M:%S')}
• 测试类型: 双邮箱对比测试
• 每个邮箱: {result['count']//2} 封邮件

📈 联通企业邮箱结果:
• 成功发送: {enterprise['success_count']}/{result['count']//2} 封
• 发送失败: {enterprise['failed_count']} 封
• 发送成功率: {enterprise['send_success_rate']:.1f}%
• 平均耗时: {enterprise['avg_send_time']:.1f} 秒/封

📧 QQ邮箱结果:
• 成功发送: {qq['success_count']}/{result['count']//2} 封
• 发送失败: {qq['failed_count']} 封
• 发送成功率: {qq['send_success_rate']:.1f}%
• 平均耗时: {qq['avg_send_time']:.1f} 秒/封

🏆 对比分析:
• 成功率对比: 企业邮箱 {enterprise['send_success_rate']:.1f}% vs QQ邮箱 {qq['send_success_rate']:.1f}%
• 速度对比: 企业邮箱 {enterprise['avg_send_time']:.1f}s vs QQ邮箱 {qq['avg_send_time']:.1f}s
• 总体成功率: {result['send_success_rate']:.1f}%
• 总耗时: {result['total_time']:.1f} 秒

💡 结论:"""

        if enterprise['send_success_rate'] > qq['send_success_rate']:
            report += "\n🏆 联通企业邮箱发送成功率更高"
        elif qq['send_success_rate'] > enterprise['send_success_rate']:
            report += "\n🏆 QQ邮箱发送成功率更高"
        else:
            report += "\n🤝 两个邮箱发送成功率相当"

        if enterprise['avg_send_time'] < qq['avg_send_time']:
            report += "\n⚡ 联通企业邮箱发送速度更快"
        elif qq['avg_send_time'] < enterprise['avg_send_time']:
            report += "\n⚡ QQ邮箱发送速度更快"
        else:
            report += "\n⚖️ 两个邮箱发送速度相当"

        # 如果有接收检测结果，显示详细统计
        if 'enterprise_received' in result:
            report += f"""

📥 接收检测结果:
• 企业邮箱接收: {result['enterprise_received']}/8 封 (丢失{result['enterprise_missing']}封)
• QQ邮箱接收: {result['qq_received']}/8 封 (丢失{result['qq_missing']}封)
• 总接收率: {result['received_count']}/{result['count']//2*2} 封 ({(result['received_count']/(result['count']//2*2)*100):.1f}%)
• 总丢邮件率: {result['loss_rate']:.1f}%

🔍 接收分析:"""

            if result['enterprise_missing'] > 0:
                report += f"\n❌ 企业邮箱丢失 {result['enterprise_missing']} 封邮件"
            else:
                report += f"\n✅ 企业邮箱接收完整"

            if result['qq_missing'] > 0:
                report += f"\n❌ QQ邮箱丢失 {result['qq_missing']} 封邮件"
            else:
                report += f"\n✅ QQ邮箱接收完整"

        report += f"\n{'='*60}"
        return report

    def add_log(self, message):
        """添加日志 - 修复版本"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        self.log_text.append(f"[{timestamp}] {message}")

        # 自动滚动到底部 - 修复QTextCursor.End问题
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)

    def clear_results(self):
        """清空结果"""
        self.result_text.clear()
        self.log_text.clear()

    def save_results(self):
        """保存结果"""
        from PySide6.QtWidgets import QFileDialog

        filename, _ = QFileDialog.getSaveFileName(
            self, "保存测试结果",
            f"email_test_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "文本文件 (*.txt)"
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.result_text.toPlainText())
                QMessageBox.information(self, "保存成功", f"结果已保存到:\n{filename}")
            except Exception as e:
                QMessageBox.critical(self, "保存失败", f"保存失败:\n{e}")

def main():
    app = QApplication(sys.argv)

    # 设置应用样式
    app.setStyleSheet("""
        QMainWindow {
            background-color: #f5f5f5;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        QLineEdit, QSpinBox, QComboBox {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        QTextEdit {
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        QProgressBar {
            border: 1px solid #ddd;
            border-radius: 3px;
            text-align: center;
        }
        QProgressBar::chunk {
            background-color: #27ae60;
            border-radius: 2px;
        }
    """)

    window = EmailTestGUI()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
